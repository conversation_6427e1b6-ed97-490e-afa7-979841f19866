# Updated FSM Protocol Diagrams

This document outlines the distinct states and transitions for the three sequential Finite State Machines that govern the agent's workflow.

## FSM 1: Discovery and Planning

This FSM is responsible for understanding the user's request, analyzing the existing codebase, and producing a detailed implementation plan.

### FSM 1: Horizontal Diagram

```mermaid
stateDiagram-v2
    direction LR
    [*] --> INITIALIZING
    INITIALIZING --> REQUIREMENT_ANALYSIS : User request received
    REQUIREMENT_ANALYSIS --> CODEBASE_EXPLORATION : Requirements understood
    CODEBASE_EXPLORATION --> PLAN_GENERATION : Codebase context gathered
    PLAN_GENERATION --> AWAITING_USER_APPROVAL : Plan created
    AWAITING_USER_APPROVAL --> FSM1_COMPLETE : Plan approved
    
    state FSM1_COMPLETE {
        direction LR
        [*] --> End
    }
```

### FSM 1: Vertical Diagram

```mermaid
stateDiagram-v2
    direction TB
    [*] --> INITIALIZING
    INITIALIZING --> REQUIREMENT_ANALYSIS : User request received
    REQUIREMENT_ANALYSIS --> CODEBASE_EXPLORATION : Requirements understood
    CODEBASE_EXPLORATION --> PLAN_GENERATION : Codebase context gathered
    PLAN_GENERATION --> AWAITING_USER_APPROVAL : Plan created
    AWAITING_USER_APPROVAL --> FSM1_COMPLETE : Plan approved
    
    state FSM1_COMPLETE {
        direction TB
        [*] --> End
    }
```

-----

## FSM 2: Implementation and Testing

This FSM executes the plan generated by FSM 1. It involves a core loop of coding, testing, and debugging until all validation checks pass.

### FSM 2: Horizontal Diagram

```mermaid
stateDiagram-v2
    direction LR
    [*] --> INITIALIZING_IMPLEMENTATION
    INITIALIZING_IMPLEMENTATION --> CODING : Plan loaded
    
    state TDD_Cycle {
        direction LR
        CODING --> VALIDATING_TESTS : Code changes applied
        VALIDATING_TESTS --> DEBUGGING : Tests or linters failed
        DEBUGGING --> CODING : Fix proposed
    }

    VALIDATING_TESTS --> FSM2_COMPLETE : All checks passed
    
    state FSM2_COMPLETE {
        direction LR
        [*] --> End
    }
```

### FSM 2: Vertical Diagram

```mermaid
stateDiagram-v2
    direction TB
    [*] --> INITIALIZING_IMPLEMENTATION
    INITIALIZING_IMPLEMENTATION --> CODING : Plan loaded
    
    state TDD_Cycle {
        direction TB
        CODING --> VALIDATING_TESTS : Code changes applied
        VALIDATING_TESTS --> DEBUGGING : Tests or linters failed
        DEBUGGING --> CODING : Fix proposed
    }

    VALIDATING_TESTS --> FSM2_COMPLETE : All checks passed
    
    state FSM2_COMPLETE {
        direction TB
        [*] --> End
    }
```

-----

## FSM 3: Finalization and Documentation

This FSM handles the final steps of the task, including updating documentation, performing legacy parity checks, and preparing the changes for a pull request.

### FSM 3: Horizontal Diagram

```mermaid
stateDiagram-v2
    direction LR
    [*] --> INITIALIZING_FINALIZATION
    INITIALIZING_FINALIZATION --> DOCUMENTING : Implementation complete
    DOCUMENTING --> LEGACY_PARITY_CHECK : Documentation updated
    LEGACY_PARITY_CHECK --> PRE_COMMIT_REVIEW : Parity confirmed
    PRE_COMMIT_REVIEW --> FSM3_COMPLETE : Final diff is clean
    
    state FSM3_COMPLETE {
        direction LR
        [*] --> End
    }
```

### FSM 3: Vertical Diagram

```mermaid
stateDiagram-v2
    direction TB
    [*] --> INITIALIZING_FINALIZATION
    INITIALIZING_FINALIZATION --> DOCUMENTING : Implementation complete
    DOCUMENTING --> LEGACY_PARITY_CHECK : Documentation updated
    LEGACY_PARITY_CHECK --> PRE_COMMIT_REVIEW : Parity confirmed
    PRE_COMMIT_REVIEW --> FSM3_COMPLETE : Final diff is clean
    
    state FSM3_COMPLETE {
        direction TB
        [*] --> End
    }
```