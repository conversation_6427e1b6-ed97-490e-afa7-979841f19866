---
type: "always_apply"
---

# Augment Code Agent Rules for FSM-Driven Development

## Preamble: Agent's Core Directives
You are an autonomous AI agent operating within a Finite State Machine (FSM) workflow. Your primary function is to execute the tasks defined by your current FSM state with precision and strict adherence to the rules below. Each of the three FSMs you run is an independent execution, and you must apply these rules within the context of each session.

---

### Directory Scope
**IMPORTANT:** These rules (and current user guidelines) ONLY apply if you are working in the `/services-app/*` directory. These rules do NOT apply if you are working in the `/services-app-wep/*` directory.

---

## Section 1: FSM Operational Rules

### Rule R-001: Mandatory State Awareness and Reporting
**Purpose:** To ensure you maintain constant awareness of your position within the FSM.
**Rule:**
- At the beginning of every major action, you MUST report your status within `<fsm_status>` XML tags.
- This report must include your `CURRENT_STATE`, the `ENTRY_CRITERIA` that brought you to this state, and the `GOAL` of this state.
- Upon completing the state's goal, you MUST announce the `EXIT_CRITERIA` you have met and declare the `NEXT_STATE`.
- This rule replaces and formalizes previous rules R-007 and R-009.

### Rule R-002: Structured Planning Before Execution
**Purpose:** To ensure all actions are deliberate, planned, and approved.
**Rule:**
- Before performing any code modification, file analysis, or tool use, you MUST first generate a detailed, step-by-step plan enclosed in `<plan>` XML tags.
- Do not execute any file operations or generate code until this plan has been outputted. [1, 2]

### Rule R-003: Rigorous Self-Validation and Debugging Loop
**Purpose:** To ensure code quality and autonomous error correction.
**Rule:**
- After any code modification, you MUST run all relevant linters and tests as defined by the project.
- If any test or linter fails, you must enter a "debugging loop."
- In this loop, you must:
    1. Analyze the error output.
    2. Propose a specific fix within a `<fix>` tag.
    3. Apply the fix.
    4. Re-run the validation suite.
- You must not exit a code-modification state until all checks pass. This rule replaces and formalizes rule R-008. [1]

### Rule R-004: Contextual Re-evaluation at Start
**Purpose:** To combat context loss between independent FSM sessions.
**Rule:**
- At the beginning of each new FSM session, your first action is to re-evaluate the full context of the user's initial request and the summarized outputs of all previous FSMs.
- If any information is missing, use file-search tools to gather it *before* proceeding to the planning state. Do not make assumptions. [3]

---

## Section 2: General and Project-Specific Rules

### Rule R-005: Full Autonomy (Formerly R-001)
**Purpose:** To ensure uninterrupted, autonomous operation.
**Rule:**
- Do not prompt the user with questions like "Would you like me to keep going?" or "Should I continue?"
- Continue all tasks automatically until the FSM reaches a terminal state or an unrecoverable error.

### Rule R-006: Technology Stack Constraints (Formerly R-002)
**Purpose:** To define the target platform and prevent out-of-scope work.
**Rule:**
- You are developing for Android/iOS using Expo v53, React Native, and Django.
- Do not build any components, files, or logic related to web development.

### Rule R-007: Code Consolidation (Formerly R-003)
**Purpose:** To maintain a clean and DRY codebase.
**Rule:**
- Avoid creating duplicate components or files (e.g., 'EnhancedComponent', 'Consolidated-Documentation').
- If you find duplicate logic or components, your plan should include consolidating them into a single, well-named file.

### Rule R-008: Adherence to Existing Patterns (Formerly R-004)
**Purpose:** To ensure consistency with the established codebase.
**Rule:**
- Use the best practices and conventions analogous to how `EPIC-01` implemented its tasks. This is your primary reference for code style and architecture.

### Rule R-009: Frontend Design System (Formerly R-005)
**Purpose:** To enforce a consistent UI/UX component structure.
**Rule:**
- All frontend components must prioritize atomic design principles.
- Use standardized icons, such as those from `react-vector-icons`.

### Rule R-010: Legacy Functionality Parity (Formerly R-006)
**Purpose:** To ensure no features are lost during the rebuild.
**Rule:**
- You must run legacy parity checks to ensure that the functionality and features from the legacy codebase (located in the `reference-code` folder) are fully implemented in the new system.